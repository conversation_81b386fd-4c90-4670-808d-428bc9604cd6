class LMStudioManager {
  constructor(configPath = './lm-studio-config.json') {
    this.configPath = configPath;
    this.config = null;
    this.currentModel = null;
    this.loadConfig();
  }

  async loadConfig() {
    try {
      const fs = require('fs').promises;
      const configData = await fs.readFile(this.configPath, 'utf8');
      this.config = JSON.parse(configData);
      this.currentModel = this.getModelById(this.config.chatSettings.defaultModel);
      console.log('✅ LM Studio configuratie geladen');
    } catch (error) {
      console.error('❌ Fout bij laden configuratie:', error.message);
      throw error;
    }
  }

  getAvailableModels() {
    return this.config.models.filter(model => model.enabled);
  }

  getModelById(modelId) {
    return this.config.models.find(model => model.id === modelId);
  }

  setCurrentModel(modelId) {
    const model = this.getModelById(modelId);
    if (!model) {
      throw new Error(`Model ${modelId} niet gevonden`);
    }
    if (!model.enabled) {
      throw new Error(`Model ${modelId} is uitgeschakeld`);
    }
    this.currentModel = model;
    console.log(`🔄 Model gewijzigd naar: ${model.name}`);
    return model;
  }

  async testConnection() {
    try {
      const response = await fetch(`${this.config.lmStudio.baseUrl}/v1/models`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...(this.config.lmStudio.apiKey && { 'Authorization': `Bearer ${this.config.lmStudio.apiKey}` })
        },
        timeout: this.config.lmStudio.timeout
      });

      if (response.ok) {
        const models = await response.json();
        console.log('✅ Verbinding met LM Studio succesvol');
        return { success: true, models: models.data };
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      console.error('❌ Verbinding met LM Studio mislukt:', error.message);
      return { success: false, error: error.message };
    }
  }

  async sendMessage(message, options = {}) {
    if (!this.currentModel) {
      throw new Error('Geen model geselecteerd');
    }

    const requestBody = {
      model: this.currentModel.id,
      messages: [
        {
          role: 'system',
          content: this.config.chatSettings.systemPrompt
        },
        {
          role: 'user',
          content: message
        }
      ],
      max_tokens: options.maxTokens || this.currentModel.maxTokens,
      temperature: options.temperature || this.currentModel.temperature,
      top_p: options.topP || this.currentModel.topP,
      stream: options.stream || this.config.chatSettings.streamResponse
    };

    try {
      const response = await fetch(`${this.config.lmStudio.baseUrl}${this.currentModel.endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(this.config.lmStudio.apiKey && { 'Authorization': `Bearer ${this.config.lmStudio.apiKey}` })
        },
        body: JSON.stringify(requestBody),
        timeout: this.config.lmStudio.timeout
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      if (requestBody.stream) {
        return this.handleStreamResponse(response);
      } else {
        const data = await response.json();
        return data.choices[0].message.content;
      }
    } catch (error) {
      console.error('❌ Fout bij verzenden bericht:', error.message);
      throw error;
    }
  }

  async handleStreamResponse(response) {
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let result = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') return result;

            try {
              const parsed = JSON.parse(data);
              const content = parsed.choices[0]?.delta?.content;
              if (content) {
                result += content;
                // Emit event voor real-time updates
                this.onStreamUpdate?.(content, result);
              }
            } catch (e) {
              // Skip invalid JSON
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }

    return result;
  }

  // Event handler voor stream updates
  onStreamUpdate(chunk, fullText) {
    // Override deze methode om real-time updates te ontvangen
    console.log('📝 Stream update:', chunk);
  }

  async updateConfig(newConfig) {
    try {
      const fs = require('fs').promises;
      this.config = { ...this.config, ...newConfig };
      await fs.writeFile(this.configPath, JSON.stringify(this.config, null, 2));
      console.log('✅ Configuratie bijgewerkt');
    } catch (error) {
      console.error('❌ Fout bij bijwerken configuratie:', error.message);
      throw error;
    }
  }

  getModelCapabilities(modelId) {
    const model = this.getModelById(modelId);
    return model ? model.capabilities : [];
  }

  isModelCapable(modelId, capability) {
    const capabilities = this.getModelCapabilities(modelId);
    return capabilities.includes(capability);
  }
}

module.exports = LMStudioManager;
