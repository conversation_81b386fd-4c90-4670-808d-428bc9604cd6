<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LM Studio AI Chat</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #1a1a1a;
            color: #ffffff;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: #2d2d2d;
            padding: 1rem;
            border-bottom: 1px solid #404040;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .model-selector {
            background: #404040;
            border: none;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
        }

        .status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
        }

        .test-button {
            background: #666;
            border: none;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.8rem;
            margin-left: 1rem;
        }

        .test-button:hover {
            background: #777;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #ff4444;
        }

        .status-dot.connected {
            background: #44ff44;
        }

        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .messages {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .message {
            max-width: 80%;
            padding: 1rem;
            border-radius: 12px;
            word-wrap: break-word;
        }

        .message.user {
            background: #0066cc;
            align-self: flex-end;
            margin-left: auto;
        }

        .message.assistant {
            background: #2d2d2d;
            align-self: flex-start;
            border: 1px solid #404040;
        }

        .message.system {
            background: #404040;
            align-self: center;
            font-style: italic;
            font-size: 0.9rem;
        }

        .input-container {
            padding: 1rem;
            background: #2d2d2d;
            border-top: 1px solid #404040;
            display: flex;
            gap: 1rem;
            align-items: flex-end;
        }

        .input-box {
            flex: 1;
            background: #404040;
            border: none;
            color: white;
            padding: 1rem;
            border-radius: 8px;
            resize: vertical;
            min-height: 50px;
            max-height: 150px;
            font-family: inherit;
        }

        .input-box:focus {
            outline: 2px solid #0066cc;
        }

        .send-button {
            background: #0066cc;
            border: none;
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: background 0.2s;
        }

        .send-button:hover:not(:disabled) {
            background: #0052a3;
        }

        .send-button:disabled {
            background: #666;
            cursor: not-allowed;
        }

        .typing-indicator {
            display: none;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem;
            color: #888;
            font-style: italic;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dots span {
            width: 6px;
            height: 6px;
            background: #888;
            border-radius: 50%;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
        .typing-dots span:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typing {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
        }

        .error-message {
            background: #ff4444;
            color: white;
            padding: 1rem;
            margin: 1rem;
            border-radius: 8px;
            display: none;
        }

        pre {
            background: #1a1a1a;
            padding: 1rem;
            border-radius: 6px;
            overflow-x: auto;
            margin: 0.5rem 0;
        }

        code {
            background: #1a1a1a;
            padding: 0.2rem 0.4rem;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', monospace;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🤖 LM Studio AI Chat</h1>
        <select class="model-selector" id="modelSelector">
            <option value="">Selecteer model...</option>
        </select>
        <div class="status">
            <div class="status-dot" id="statusDot"></div>
            <span id="statusText">Niet verbonden</span>
        </div>
        <button class="test-button" id="testButton">Test Verbinding</button>
    </div>

    <div class="error-message" id="errorMessage"></div>

    <div class="chat-container">
        <div class="messages" id="messages">
            <div class="message system">
                Welkom bij LM Studio AI Chat! Selecteer een model om te beginnen.
            </div>
        </div>

        <div class="typing-indicator" id="typingIndicator">
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
            AI is aan het typen...
        </div>

        <div class="input-container">
            <textarea 
                class="input-box" 
                id="messageInput" 
                placeholder="Typ je bericht hier... (Shift+Enter voor nieuwe regel, Enter om te verzenden)"
                rows="1"
            ></textarea>
            <button class="send-button" id="sendButton" disabled>Verzenden</button>
        </div>
    </div>

    <script src="chat-client.js"></script>
</body>
</html>
