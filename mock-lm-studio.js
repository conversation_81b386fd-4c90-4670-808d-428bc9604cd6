const http = require('http');

// Mock LM Studio server voor testing
const mockServer = http.createServer((req, res) => {
    // CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    
    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }

    if (req.url === '/v1/models' && req.method === 'GET') {
        // Mock models endpoint
        const response = {
            data: [
                {
                    id: "deepseek-v3",
                    object: "model",
                    created: Date.now(),
                    owned_by: "lm-studio"
                }
            ]
        };
        
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify(response));
        return;
    }

    if (req.url === '/v1/chat/completions' && req.method === 'POST') {
        // Mock chat completions endpoint
        let body = '';
        req.on('data', chunk => {
            body += chunk.toString();
        });
        
        req.on('end', () => {
            try {
                const requestData = JSON.parse(body);
                const userMessage = requestData.messages[requestData.messages.length - 1].content;
                
                // Mock AI response
                const mockResponse = {
                    id: "chatcmpl-" + Date.now(),
                    object: "chat.completion",
                    created: Date.now(),
                    model: requestData.model,
                    choices: [{
                        index: 0,
                        message: {
                            role: "assistant",
                            content: `🤖 Mock AI Response: Ik heb je bericht ontvangen: "${userMessage}"\n\nDit is een test response van de mock server. Zodra je LM Studio start, krijg je echte AI responses!\n\n**Wat je kunt doen:**\n- Start LM Studio\n- Laad een model\n- Start de Local Server\n- Probeer opnieuw te chatten`
                        },
                        finish_reason: "stop"
                    }],
                    usage: {
                        prompt_tokens: 50,
                        completion_tokens: 100,
                        total_tokens: 150
                    }
                };
                
                res.writeHead(200, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify(mockResponse));
            } catch (error) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ error: 'Invalid JSON' }));
            }
        });
        return;
    }

    // 404 for other endpoints
    res.writeHead(404, { 'Content-Type': 'text/plain' });
    res.end('Not Found');
});

mockServer.listen(1234, () => {
    console.log('🎭 Mock LM Studio server draait op http://localhost:1234');
    console.log('📝 Dit is alleen voor testing - start echte LM Studio voor AI responses');
});

module.exports = mockServer;
