{"lmStudio": {"baseUrl": "http://localhost:1234", "apiKey": "", "timeout": 30000, "maxRetries": 3}, "models": [{"id": "llava-v1.5-7b-llamafile", "name": "LLaVA v1.5 7B", "description": "Vision-language model voor afbeeldingen en tekst", "provider": "lm-studio", "endpoint": "/v1/chat/completions", "maxTokens": 4096, "temperature": 0.4, "topP": 0.9, "enabled": true, "capabilities": ["chat", "vision", "multimodal"]}, {"id": "deepseek-r1-8528-qwen3-8b-mlx8bit", "name": "DeepSeek R1 8B", "description": "Advanced reasoning model voor complexe problemen", "provider": "lm-studio", "endpoint": "/v1/chat/completions", "maxTokens": 8192, "temperature": 0.3, "topP": 0.9, "enabled": true, "capabilities": ["chat", "reasoning", "analysis"]}, {"id": "llama-2-13b-chat", "name": "Llama 2 13B Chat", "description": "Krachtig chat model voor algemene conversaties", "provider": "lm-studio", "endpoint": "/v1/chat/completions", "maxTokens": 4096, "temperature": 0.7, "topP": 0.9, "enabled": true, "capabilities": ["chat", "conversation", "general"]}, {"id": "deepseek-coder-6.7b-kexer", "name": "DeepSeek Coder 6.7B", "description": "Gespecialiseerd model voor programmeren en code", "provider": "lm-studio", "endpoint": "/v1/chat/completions", "maxTokens": 4096, "temperature": 0.2, "topP": 0.95, "enabled": true, "capabilities": ["code", "programming", "debugging"]}, {"id": "llava-v1.5-7b-llamafile2", "name": "LLaVA v1.5 7B (v2)", "description": "Alternatieve versie van vision-language model", "provider": "lm-studio", "endpoint": "/v1/chat/completions", "maxTokens": 4096, "temperature": 0.4, "topP": 0.9, "enabled": true, "capabilities": ["chat", "vision", "multimodal"]}], "chatSettings": {"defaultModel": "deepseek-v3", "systemPrompt": "Je bent een behulpzame AI-assistent die kan helpen met <PERSON><PERSON>, vragen beantwoorden en taken uitvoeren.", "maxHistoryLength": 20, "streamResponse": true, "autoSave": true}, "ui": {"theme": "dark", "showModelSelector": true, "showTokenCount": true, "enableVoiceInput": true, "enableFileUpload": true}}