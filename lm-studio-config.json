{"lmStudio": {"baseUrl": "http://localhost:1234", "apiKey": "", "timeout": 30000, "maxRetries": 3}, "models": [{"id": "deepseek-v3", "name": "DeepSeek V3", "description": "Advanced reasoning model", "provider": "lm-studio", "endpoint": "/v1/chat/completions", "maxTokens": 4096, "temperature": 0.7, "topP": 0.9, "enabled": true, "capabilities": ["chat", "code", "reasoning"]}, {"id": "llama-3.1-8b", "name": "Llama 3.1 8B", "description": "Fast and efficient model for general tasks", "provider": "lm-studio", "endpoint": "/v1/chat/completions", "maxTokens": 2048, "temperature": 0.7, "topP": 0.9, "enabled": false, "capabilities": ["chat", "code"]}, {"id": "codellama-13b", "name": "Code Llama 13B", "description": "Specialized model for code generation", "provider": "lm-studio", "endpoint": "/v1/chat/completions", "maxTokens": 4096, "temperature": 0.3, "topP": 0.95, "enabled": false, "capabilities": ["code", "debugging", "explanation"]}], "chatSettings": {"defaultModel": "deepseek-v3", "systemPrompt": "Je bent een behulpzame AI-assistent die kan helpen met <PERSON><PERSON>, vragen beantwoorden en taken uitvoeren.", "maxHistoryLength": 20, "streamResponse": true, "autoSave": true}, "ui": {"theme": "dark", "showModelSelector": true, "showTokenCount": true, "enableVoiceInput": true, "enableFileUpload": true}}