class ChatClient {
    constructor() {
        this.config = null;
        this.currentModel = null;
        this.isConnected = false;
        this.conversationHistory = [];
        
        this.initializeElements();
        this.setupEventListeners();
        this.loadConfig();
    }

    initializeElements() {
        this.modelSelector = document.getElementById('modelSelector');
        this.statusDot = document.getElementById('statusDot');
        this.statusText = document.getElementById('statusText');
        this.messages = document.getElementById('messages');
        this.messageInput = document.getElementById('messageInput');
        this.sendButton = document.getElementById('sendButton');
        this.typingIndicator = document.getElementById('typingIndicator');
        this.errorMessage = document.getElementById('errorMessage');
    }

    setupEventListeners() {
        this.sendButton.addEventListener('click', () => this.sendMessage());
        this.messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });
        
        this.messageInput.addEventListener('input', () => {
            this.autoResize();
        });

        this.modelSelector.addEventListener('change', (e) => {
            this.selectModel(e.target.value);
        });
    }

    async loadConfig() {
        try {
            const response = await fetch('./lm-studio-config.json');
            this.config = await response.json();
            this.populateModelSelector();
            this.testConnection();
        } catch (error) {
            this.showError('Fout bij laden configuratie: ' + error.message);
        }
    }

    populateModelSelector() {
        this.modelSelector.innerHTML = '<option value="">Selecteer model...</option>';
        
        const enabledModels = this.config.models.filter(model => model.enabled);
        enabledModels.forEach(model => {
            const option = document.createElement('option');
            option.value = model.id;
            option.textContent = `${model.name} - ${model.description}`;
            this.modelSelector.appendChild(option);
        });

        // Selecteer default model
        if (this.config.chatSettings.defaultModel) {
            this.modelSelector.value = this.config.chatSettings.defaultModel;
            this.selectModel(this.config.chatSettings.defaultModel);
        }
    }

    selectModel(modelId) {
        if (!modelId) {
            this.currentModel = null;
            this.sendButton.disabled = true;
            return;
        }

        this.currentModel = this.config.models.find(model => model.id === modelId);
        if (this.currentModel) {
            this.sendButton.disabled = false;
            this.addSystemMessage(`Model gewijzigd naar: ${this.currentModel.name}`);
        }
    }

    async testConnection() {
        try {
            const response = await fetch(`${this.config.lmStudio.baseUrl}/v1/models`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    ...(this.config.lmStudio.apiKey && { 
                        'Authorization': `Bearer ${this.config.lmStudio.apiKey}` 
                    })
                }
            });

            if (response.ok) {
                this.setConnectionStatus(true, 'Verbonden met LM Studio');
            } else {
                throw new Error(`HTTP ${response.status}`);
            }
        } catch (error) {
            this.setConnectionStatus(false, 'Verbinding mislukt');
            this.showError('Kan geen verbinding maken met LM Studio. Zorg ervoor dat LM Studio draait op ' + this.config.lmStudio.baseUrl);
        }
    }

    setConnectionStatus(connected, message) {
        this.isConnected = connected;
        this.statusDot.classList.toggle('connected', connected);
        this.statusText.textContent = message;
    }

    async sendMessage() {
        const message = this.messageInput.value.trim();
        if (!message || !this.currentModel || !this.isConnected) return;

        this.addUserMessage(message);
        this.messageInput.value = '';
        this.autoResize();
        this.showTyping(true);

        try {
            const response = await this.callLMStudio(message);
            this.addAssistantMessage(response);
        } catch (error) {
            this.showError('Fout bij verzenden bericht: ' + error.message);
            this.addSystemMessage('❌ Bericht kon niet worden verzonden');
        } finally {
            this.showTyping(false);
        }
    }

    async callLMStudio(message) {
        const requestBody = {
            model: this.currentModel.id,
            messages: [
                {
                    role: 'system',
                    content: this.config.chatSettings.systemPrompt
                },
                ...this.conversationHistory,
                {
                    role: 'user',
                    content: message
                }
            ],
            max_tokens: this.currentModel.maxTokens,
            temperature: this.currentModel.temperature,
            top_p: this.currentModel.topP,
            stream: false // Voor eenvoud, geen streaming in deze versie
        };

        const response = await fetch(`${this.config.lmStudio.baseUrl}${this.currentModel.endpoint}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                ...(this.config.lmStudio.apiKey && { 
                    'Authorization': `Bearer ${this.config.lmStudio.apiKey}` 
                })
            },
            body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        return data.choices[0].message.content;
    }

    addUserMessage(message) {
        this.addMessage('user', message);
        this.conversationHistory.push({ role: 'user', content: message });
        this.trimHistory();
    }

    addAssistantMessage(message) {
        this.addMessage('assistant', message);
        this.conversationHistory.push({ role: 'assistant', content: message });
        this.trimHistory();
    }

    addSystemMessage(message) {
        this.addMessage('system', message);
    }

    addMessage(type, content) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}`;
        
        // Basis markdown ondersteuning
        const formattedContent = this.formatMessage(content);
        messageDiv.innerHTML = formattedContent;
        
        this.messages.appendChild(messageDiv);
        this.scrollToBottom();
    }

    formatMessage(content) {
        // Basis markdown formatting
        return content
            .replace(/```(\w+)?\n([\s\S]*?)```/g, '<pre><code>$2</code></pre>')
            .replace(/`([^`]+)`/g, '<code>$1</code>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/\n/g, '<br>');
    }

    trimHistory() {
        const maxLength = this.config.chatSettings.maxHistoryLength;
        if (this.conversationHistory.length > maxLength) {
            this.conversationHistory = this.conversationHistory.slice(-maxLength);
        }
    }

    showTyping(show) {
        this.typingIndicator.style.display = show ? 'flex' : 'none';
        if (show) this.scrollToBottom();
    }

    showError(message) {
        this.errorMessage.textContent = message;
        this.errorMessage.style.display = 'block';
        setTimeout(() => {
            this.errorMessage.style.display = 'none';
        }, 5000);
    }

    autoResize() {
        this.messageInput.style.height = 'auto';
        this.messageInput.style.height = this.messageInput.scrollHeight + 'px';
    }

    scrollToBottom() {
        setTimeout(() => {
            this.messages.scrollTop = this.messages.scrollHeight;
        }, 100);
    }
}

// Start de chat client wanneer de pagina geladen is
document.addEventListener('DOMContentLoaded', () => {
    new ChatClient();
});
