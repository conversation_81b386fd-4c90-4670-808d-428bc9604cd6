# LM Studio AI Chat Interface

Een eenvoudige web-interface voor het chatten met LM Studio modellen.

## 🚀 Snelle Start

### 1. LM Studio Instellen

1. Download en installeer [LM Studio](https://lmstudio.ai/)
2. Download een model (bijvoorbeeld DeepSeek V3, Llama 3.1, Code Llama)
3. Start LM Studio en laad een model
4. Ga naar de "Local Server" tab
5. Klik op "Start Server" (standaard op poort 1234)

### 2. Chat Interface Starten

```bash
# Start de webserver
node server.js
```

De interface is nu beschikbaar op: http://localhost:3000

## 📁 Bestanden Overzicht

- `lm-studio-config.json` - Configuratie voor modellen en instellingen
- `lm-studio-manager.js` - JavaScript klasse voor LM Studio integratie
- `chat-interface.html` - Web interface voor de chat
- `chat-client.js` - Client-side JavaScript voor de chat functionaliteit
- `server.js` - Eenvoudige HTTP server om de bestanden te serveren

## ⚙️ Configuratie

### Model Configuratie

Bewerk `lm-studio-config.json` om modellen toe te voegen of aan te passen:

```json
{
  "models": [
    {
      "id": "jouw-model-id",
      "name": "Jouw Model Naam",
      "description": "Beschrijving van het model",
      "provider": "lm-studio",
      "endpoint": "/v1/chat/completions",
      "maxTokens": 4096,
      "temperature": 0.7,
      "topP": 0.9,
      "enabled": true,
      "capabilities": ["chat", "code", "reasoning"]
    }
  ]
}
```

### LM Studio Instellingen

```json
{
  "lmStudio": {
    "baseUrl": "http://localhost:1234",
    "apiKey": "",
    "timeout": 30000,
    "maxRetries": 3
  }
}
```

### Chat Instellingen

```json
{
  "chatSettings": {
    "defaultModel": "deepseek-v3",
    "systemPrompt": "Je bent een behulpzame AI-assistent...",
    "maxHistoryLength": 20,
    "streamResponse": true,
    "autoSave": true
  }
}
```

## 🔧 Gebruik

1. **Model Selecteren**: Kies een model uit de dropdown in de header
2. **Verbinding Controleren**: De status indicator toont of de verbinding met LM Studio actief is
3. **Chatten**: Typ je bericht en druk op Enter of klik op "Verzenden"
4. **Keyboard Shortcuts**:
   - `Enter`: Verzend bericht
   - `Shift + Enter`: Nieuwe regel

## 🎯 Features

- ✅ Meerdere modellen ondersteuning
- ✅ Real-time verbindingsstatus
- ✅ Markdown ondersteuning in berichten
- ✅ Conversatie geschiedenis
- ✅ Responsive design
- ✅ Dark theme
- ✅ Error handling
- ✅ Typing indicator

## 🛠️ Programmatisch Gebruik

Je kunt ook de `LMStudioManager` klasse direct gebruiken in je eigen projecten:

```javascript
const LMStudioManager = require('./lm-studio-manager');

async function example() {
    const manager = new LMStudioManager();
    
    // Test verbinding
    const connection = await manager.testConnection();
    console.log('Verbinding:', connection);
    
    // Selecteer model
    manager.setCurrentModel('deepseek-v3');
    
    // Verstuur bericht
    const response = await manager.sendMessage('Hallo, kun je me helpen met programmeren?');
    console.log('Antwoord:', response);
}

example().catch(console.error);
```

## 🔍 Troubleshooting

### Verbindingsproblemen

1. **LM Studio draait niet**: Start LM Studio en zorg dat de server actief is
2. **Verkeerde poort**: Controleer of LM Studio op poort 1234 draait
3. **CORS errors**: De server heeft CORS headers ingesteld, maar controleer je browser console

### Model Problemen

1. **Model niet beschikbaar**: Zorg dat het model geladen is in LM Studio
2. **Langzame responses**: Verhoog de timeout in de configuratie
3. **Out of memory**: Gebruik een kleiner model of verhoog je systeem RAM

### Interface Problemen

1. **Pagina laadt niet**: Controleer of `node server.js` draait
2. **JavaScript errors**: Open browser developer tools voor meer details
3. **Styling problemen**: Hard refresh (Ctrl+F5) om cache te legen

## 📝 Aanpassingen

### Nieuwe Modellen Toevoegen

1. Voeg het model toe aan `lm-studio-config.json`
2. Zet `enabled: true`
3. Herstart de interface

### UI Aanpassen

- Bewerk `chat-interface.html` voor HTML structuur
- Pas CSS in de `<style>` sectie aan voor styling
- Wijzig `chat-client.js` voor functionaliteit

### API Uitbreiden

- Gebruik `lm-studio-manager.js` als basis
- Voeg nieuwe methoden toe voor extra functionaliteit
- Implementeer streaming responses voor real-time updates

## 🤝 Bijdragen

Voel je vrij om issues te rapporteren of pull requests te maken voor verbeteringen!

## 📄 Licentie

MIT License - gebruik vrij in je eigen projecten.
