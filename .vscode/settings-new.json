{"chat.edits2.enabled": true, "chat.mcp.discovery.enabled": true, "chat.mcp.serverSampling": {}, "cursor.chat.models": [{"name": "LLaVA v1.5 7B (Vision)", "provider": "openai-compatible", "baseUrl": "http://localhost:1234/v1", "model": "llava-v1.5-7b-llamafile", "apiKey": ""}, {"name": "DeepSeek R1 8B (Reasoning)", "provider": "openai-compatible", "baseUrl": "http://localhost:1234/v1", "model": "deepseek-r1-8528-qwen3-8b-mlx8bit", "apiKey": ""}, {"name": "Llama 2 13B Chat", "provider": "openai-compatible", "baseUrl": "http://localhost:1234/v1", "model": "llama-2-13b-chat", "apiKey": ""}, {"name": "DeepSeek Coder 6.7B", "provider": "openai-compatible", "baseUrl": "http://localhost:1234/v1", "model": "deepseek-coder-6.7b-kexer", "apiKey": ""}, {"name": "LLaVA v1.5 7B (v2)", "provider": "openai-compatible", "baseUrl": "http://localhost:1234/v1", "model": "llava-v1.5-7b-llamafile2", "apiKey": ""}]}