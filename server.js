const http = require('http');
const fs = require('fs').promises;
const path = require('path');

class SimpleServer {
    constructor(port = 8080) {
        this.port = port;
        this.mimeTypes = {
            '.html': 'text/html',
            '.js': 'text/javascript',
            '.css': 'text/css',
            '.json': 'application/json',
            '.png': 'image/png',
            '.jpg': 'image/jpeg',
            '.gif': 'image/gif',
            '.ico': 'image/x-icon'
        };
    }

    async start() {
        const server = http.createServer(async (req, res) => {
            await this.handleRequest(req, res);
        });

        server.listen(this.port, '127.0.0.1', () => {
            console.log(`🚀 Server draait op http://127.0.0.1:${this.port}`);
            console.log(`🌐 Ook beschikbaar op http://localhost:${this.port}`);
            console.log('📁 Bestanden worden geserveerd vanuit de huidige directory');
            console.log('🔧 Zorg ervoor dat LM Studio draait op http://localhost:1234');
        });

        return server;
    }

    async handleRequest(req, res) {
        try {
            let filePath = req.url === '/' ? '/chat-interface.html' : req.url;
            filePath = path.join(__dirname, filePath);

            // Beveiligingscheck
            if (!filePath.startsWith(__dirname)) {
                this.sendError(res, 403, 'Forbidden');
                return;
            }

            const stats = await fs.stat(filePath);
            if (stats.isFile()) {
                await this.serveFile(res, filePath);
            } else {
                this.sendError(res, 404, 'File not found');
            }
        } catch (error) {
            if (error.code === 'ENOENT') {
                this.sendError(res, 404, 'File not found');
            } else {
                console.error('Server error:', error);
                this.sendError(res, 500, 'Internal server error');
            }
        }
    }

    async serveFile(res, filePath) {
        const ext = path.extname(filePath);
        const mimeType = this.mimeTypes[ext] || 'text/plain';

        const content = await fs.readFile(filePath);
        
        res.writeHead(200, {
            'Content-Type': mimeType,
            'Content-Length': content.length,
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        });
        
        res.end(content);
    }

    sendError(res, statusCode, message) {
        res.writeHead(statusCode, { 'Content-Type': 'text/plain' });
        res.end(message);
    }
}

// Start de server
if (require.main === module) {
    const server = new SimpleServer(8080);
    server.start().catch(console.error);
}

module.exports = SimpleServer;
